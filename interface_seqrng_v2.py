#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Sep  1 19:52:36 2024

@author: swalborn
"""
from urllib3.exceptions import InsecureRequestWarning
import requests
import json
import base64
import os

###########################################################
def convert_byte_alphanumeric(byte_string):
    
    alphanumeric_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#"
       
    # Convert the binary data to a string of alphanumeric characters
    alphanumeric_string = ""
    for byte in byte_string:
        # Map each byte to an index in the alphanumeric characters
        index = byte % len(alphanumeric_chars)
        alphanumeric_string += alphanumeric_chars[index]

    return alphanumeric_string
#######################################################################
def entropy_check(entropy):
    if entropy >0.707:
        return 'success'
    else:
        return 'failure'
#################CALL Routine SeQRNG to get byte data with self testing report ###################
def sq_get_random_bytes(num_bytes, packages, base_url, api_token):
    requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    size=num_bytes
    numpack=packages
    token = 'Bearer '+api_token
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/get_data"
    headers = {'Authorization': token}
    data = {'request_type': 'data',
            'package_size': size,
            'package_total': numpack,
            'output_type': 'base64',
            'self_testing_report': '1'}
    try:
        response = requests.post(api_url, headers=headers, data=data,verify=False)
        response.raise_for_status()
        content = json.loads(response.content)
        rnd_str = content['success']['base64_data']
        # Self testing data
        sprstr = content['success']['self_testing_report'][4]['Statistics']['SPR']
        etystr = content['success']['self_testing_report'][4]['Statistics']['ETY']
        murstr = content['success']['self_testing_report'][4]['Statistics']['MUR']
        visstr = content['success']['self_testing_report'][4]['Statistics']['VIS']
        g2cstr = content['success']['self_testing_report'][4]['Statistics']['G2C']
        kbsstr = content['success']['self_testing_report'][4]['Statistics']['KBS']
        spr=float(sprstr)
        ety=float(etystr)
        vis=float(visstr)
        mur=float(murstr)
        g2c=float(g2cstr)
        kbs=float(kbsstr)
        etystatus=entropy_check(ety)
        #err is 1-err = fidelity
        err=0.5+0.5*vis
        
        errstr=f"{err:.7}"[:7]
         # Decode base64 to bytes
        decoded_bytes = base64.b64decode(rnd_str)
        
        if len(decoded_bytes) != num_bytes*numpack:
            raise ValueError(f"Received {len(decoded_bytes)} bytes, expected {num_bytes}")
        # Decode bytes to hexadecimal format
        
        return(decoded_bytes,errstr,etystr,etystatus)
        
    except Exception as e:
        print({e})
        print("Using local backup PRNG.")
        prng_bytes = os.urandom(num_bytes)
        return (prng_bytes,"N/A - prng output", "N/A - prng output", "N/A - prng output")
####################################################################################    
#################CALL Routine SeQRNG to get random hexadecimal key###################
def get_random_hex_key(num_bytes, base_url, api_token):
    size=num_bytes
    numpack=1
    token = 'Bearer '+api_token
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address  
        api_url = f"http://{base_url}/api/v1/get_data"
    headers = {'Authorization': token}
    data = {'request_type': 'data',
            'package_size': size,
            'package_total': numpack,
            'output_type': 'base64',
            'self_testing_report': '1'}
    try:
        response = requests.post(api_url, headers=headers, data=data)
        response.raise_for_status()
        content = json.loads(response.content)
        rnd_str = content['success']['base64_data']
        # Self testing data
        sprstr = content['success']['self_testing_report'][4]['Statistics']['SPR']
        etystr = content['success']['self_testing_report'][4]['Statistics']['ETY']
        murstr = content['success']['self_testing_report'][4]['Statistics']['MUR']
        visstr = content['success']['self_testing_report'][4]['Statistics']['VIS']
        g2cstr = content['success']['self_testing_report'][4]['Statistics']['G2C']
        kbsstr = content['success']['self_testing_report'][4]['Statistics']['KBS']
        spr=float(sprstr)
        ety=float(etystr)
        vis=float(visstr)
        mur=float(murstr)
        g2c=float(g2cstr)
        kbs=float(kbsstr)
        etystatus=entropy_check(ety)
        #err is 1-err = fidelity
        err=0.5+0.5*vis
        
        errstr=f"{err:.7}"[:7]
         # Decode base64 to bytes
        decoded_bytes = base64.b64decode(rnd_str)
        
        if len(decoded_bytes) != num_bytes:
            raise ValueError(f"Received {len(decoded_bytes)} bytes, expected {num_bytes}")
        # Decode bytes to hexadecimal format
        rnd_hex = decoded_bytes.hex()
        return(rnd_hex,errstr,etystr,etystatus)
        
    except Exception as e:
        print({e})
        print("Using local backup RNG.")
        prng_bytes = os.urandom(num_bytes)
        prng_hex= prng_bytes.hex()
        return (prng_hex,"N/A - prng output", "N/A - prng output", "N/A - prng output")
####################################################################################

#################CALL Routine SeQRNG to get alphanumeric key###################
def get_random_alphanumeric_key(num_bytes, base_url, api_token):
    size=num_bytes
    numpack=1
    token = 'Bearer '+api_token
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address  
        api_url = f"http://{base_url}/api/v1/get_data"
    headers = {'Authorization': token}
    data = {'request_type': 'data',
            'package_size': size,
            'package_total': numpack,
            'output_type': 'base64',
            'self_testing_report': '1'}
    try:
        response = requests.post(api_url, headers=headers, data=data)
        response.raise_for_status()
        content = json.loads(response.content)
        rnd_str = content['success']['base64_data']
        # Self testing data
        sprstr = content['success']['self_testing_report'][4]['Statistics']['SPR']
        etystr = content['success']['self_testing_report'][4]['Statistics']['ETY']
        murstr = content['success']['self_testing_report'][4]['Statistics']['MUR']
        visstr = content['success']['self_testing_report'][4]['Statistics']['VIS']
        g2cstr = content['success']['self_testing_report'][4]['Statistics']['G2C']
        kbsstr = content['success']['self_testing_report'][4]['Statistics']['KBS']
        spr=float(sprstr)
        ety=float(etystr)
        vis=float(visstr)
        mur=float(murstr)
        g2c=float(g2cstr)
        kbs=float(kbsstr)
        etystatus=entropy_check(ety)
         #err is 1-err = fidelity
        err=0.5+0.5*vis
        errstr=f"{err:.7}"[:7]
         # Decode base64 to bytes
        decoded_bytes = base64.b64decode(rnd_str)
        # Calculating the length of the provided string
        
        if len(decoded_bytes) != num_bytes:
            raise ValueError(f"Received {len(decoded_bytes)} bytes, expected {num_bytes}")
        # Decode bytes to hexadecimal format
        #allowed_characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-={}[]:;\"'<>,.?/|\\~`"
        
        alphanumeric_key = convert_byte_alphanumeric(decoded_bytes)
    
        return(alphanumeric_key,errstr,etystr,etystatus)
        
    except Exception as e:
        print({e})
        print("Using local backup RNG.")
        prng_bytes = os.urandom(num_bytes)
        prng_alphanumeric_key = convert_byte_alphanumeric(decoded_bytes)
        return (prng_alphanumeric_key,"N/A - prng output", "N/A - prng output", "N/A - prng output")
####################################################################################