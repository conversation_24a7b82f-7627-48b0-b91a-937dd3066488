﻿from urllib3.exceptions import InsecureRequestWarning
import requests
import random
import base64
import time
import os
import sys
import json
import gc

from interface_seqrng_v2 import sq_get_random_bytes
from sq_ctm_modules_v1 import get_valid_input, get_integer_input, str_to_boolean, group_bytes, check_key_length, ctm_upload_key, ctm_get_api_key, ctm_key_exists
from config import get_config, interactive_setup
#from sq_ctm_testing_v1 import get_pseudo_random_bytes

coding = "latin-1"

def load_configuration():
    """
    Load and validate configuration from secure sources
    Returns configuration dictionaries for SeQRNG and CTM
    """
    try:
        config = get_config()
        
        # Validate configuration
        if not config.validate_config():
            print("\n❌ Configuration validation failed!")
            print("\n🔧 Available setup options:")
            print("1. Run: python config.py --setup (interactive setup)")
            print("2. Run: python config.py --create-examples (create example files)")
            print("3. Set environment variables manually")
            print("4. Create .env file manually")
            sys.exit(1)
        
        # Get configuration
        seqrng_config = config.get_seqrng_config()
        ctm_config = config.get_ctm_config()
        
        print("✅ Configuration loaded successfully")
        print(f"📡 SeQRNG: {seqrng_config['base_url']}")
        print(f"🔐 CTM: {ctm_config['base_url']}")
        
        return seqrng_config, ctm_config
        
    except Exception as e:
        print(f"\n❌ Configuration error: {e}")
        print("\n🔧 To fix this, run one of the following:")
        print("  python config.py --setup")
        print("  python config.py --create-examples")
        sys.exit(1)

def main():
    # Load secure configuration
    seqrng_config, ctm_config = load_configuration()
    
    # Extract configuration values
    sq_base_url = seqrng_config['base_url']
    sq_api_token = seqrng_config['api_token']
    
    ctm_base_url = ctm_config['base_url'] 
    ctm_login_data = {
        "name": ctm_config['username'],
        "password": ctm_config['password'],
        "domain": ctm_config['domain']
    }
    
    # Check if enough arguments are provided
    #if len(sys.argv) != 3:
       # print("Usage: upload key material from SeQRNG to CTM")
       # sys.exit(1)

    # Parse numbers
    #algo = sys.argv[1]
    #keyname = sys.argv[2]
    #numbytes = int(sys.argv[3])
    
    print("This program will create quantum keys and upload them to Thales Ciphertrust Manager.  Single and Multiple Key Upload is available.  Please provide the following information.")
    #get domain in CTM
    #domain = get_valid_input(["Sequre_Quantum_test"],"Enter CTM domain:","Sequre_Quantum_test")
   
   
     #Input algorithm type
    algo = get_valid_input(["aes","aria","hmac-sha1","hmac-sha256","hmac-sha384","hmac-sha512"],"Choose an algorithm", "aes")
    # Input acceptable number of bytes in key for each algorithm
    if algo == "aes":
        numbytes=int(get_valid_input(["16","24","32"],"Choose number of bytes in key","32"))
    elif algo == "aria":
        numbytes=int(get_valid_input(["16","24","32"],"Choose number of bytes in key"))
    elif algo == "hmac-sha1":
        numbytes=int(get_valid_input(["16","24","32"],"Choose number of bytes in key"))
    elif algo == "hmac-sha256":
        numbytes=int(get_valid_input(["16","24","32","64"],"Choose number of bytes in key"))  
    elif algo == "hmac-sha384":
        numbytes=int(get_valid_input(["24","36","48"],"Choose number of bytes in key")) 
    elif algo == "hmac-sha512":
        numbytes=int(get_valid_input(["32","48","64"],"Choose number of bytes in key")) 
    else:
        numbytes = int(input("Enter the number of bytes in key (32 bytes = 256 bits): "))
    owner = input("Enter the key owner: ")
    exportable = str_to_boolean(get_valid_input(["true","false"],"Key(s) is unexportable?","true"))
    operation = get_valid_input(["single","multiple"],"Enter key upload mode","single")
   
    if operation == "single":
        try:
            keyname = input("Enter the key name: ") 
            #get random bytes from SeQRNG
            qrng_key, errstr, etystr, etystatus = sq_get_random_bytes(numbytes, 1, sq_base_url, sq_api_token)
           
        
            if check_key_length(qrng_key,numbytes):
                #get API key from CTM
                #check_algorithm_key_bytes(qrng_key, algo)
                ctm_api_key = ctm_get_api_key(ctm_base_url, ctm_login_data)
                if ctm_key_exists(ctm_base_url, ctm_api_key, keyname):
                    print("Key name already exists... try with another name")
                    sys.exit(2)
                
                #Import byte key routine for aes, hmac-sha1, hmac-sha256, hmac-sha384, hmac-sha512
                #ctm_upload_key(ip_address, api_key, key_id, key_material, algorithm)
                # define algorithm as string
                ctm_upload_key(ctm_base_url, ctm_api_key, keyname, qrng_key, algo, owner, exportable)
            else:
                print("Error: Key not generated or less than required bytes. Exit program.")
                sys.exit(0)  
        except ValueError:
            print("A Value error occurred.")
    elif operation == "multiple":
        try:
            # Ask for two strings
            print("Key name will be 'BASE_IDNumber', where IDNumber is numerical sequence or random")
            key_base = input("Enter base for key names: ")
            multiple_name = get_valid_input(["s","r"],"Enter type of multiple key labelling (s=sequential, r=random)")
            if multiple_name=="s":
                key_num_min = get_integer_input(1,100000,"Enter first (lowest) key ID number")
                key_num_max = get_integer_input(int(key_num_min),100000,"Enter last key ID number")
                num_keys=key_num_max-key_num_min+1
                #qrng_keys=get_pseudo_random_bytes(numbytes, num_keys)
                qrng_keys, errstr, etystr, etystatus = sq_get_random_bytes(numbytes, num_keys, sq_base_url, sq_api_token)
               
                key_list=group_bytes(qrng_keys,numbytes, num_keys)
                ctm_api_key = ctm_get_api_key(ctm_base_url, ctm_login_data)
                ctr=0
                for round in range(key_num_min,key_num_max+1):
                    if check_key_length(key_list[ctr],numbytes):
                        keyname=key_base+str(round)
                        # Verificar si la clave ya existe antes de subirla
                        if ctm_key_exists(ctm_base_url, ctm_api_key, keyname):
                            print(f"Key name '{keyname}' already exists. Skipping this key.")
                            ctr+=1
                            continue
                        ctm_upload_key(ctm_base_url, ctm_api_key, keyname, key_list[ctr], algo, owner, exportable)
                    else:
                        print("Error: Key not generated or less than required bytes. Exit program.")
                        sys.exit(0)  
                    ctr+=1    
            elif multiple_name=="r":    
                num_keys = get_integer_input(1,100,"How many keys to create and upload to CTM")
                #qrng_keys=get_pseudo_random_bytes(numbytes, num_keys)
                qrng_keys, errstr, etystr, etystatus = sq_get_random_bytes(numbytes, num_keys, sq_base_url, sq_api_token)
                
                key_list=group_bytes(qrng_keys,numbytes, num_keys)
                ctm_api_key = ctm_get_api_key(ctm_base_url, ctm_login_data)
                for round in range(num_keys):
                    if check_key_length(key_list[round],numbytes):
                        # Generar nombre único con reintentos
                        max_attempts = 10
                        attempts = 0
                        while attempts < max_attempts:
                            keyname=key_base+str(random.randint(100000,999999))
                            if not ctm_key_exists(ctm_base_url, ctm_api_key, keyname):
                                break
                            attempts += 1
                            print(f"Key name '{keyname}' already exists, generating new name... (attempt {attempts}/{max_attempts})")
                        
                        if attempts >= max_attempts:
                            print(f"Could not generate unique key name after {max_attempts} attempts. Skipping key {round+1}")
                            continue
                            
                        ctm_upload_key(ctm_base_url, ctm_api_key, keyname, key_list[round], algo, owner, exportable)
                    else:
                        print("Error: Key not generated or less than required bytes. Exit program.")
                        sys.exit(0)      
        except ValueError:
            print("A Value error occurred.")

    gc.collect()

if __name__ == "__main__":
    main()