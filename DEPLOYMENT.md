# 🚀 SeQRNG-CTM Deployment Guide

## 📋 Prerequisites

- Python 3.7 or higher
- Access to SeQRNG device
- Access to Thales CipherTrust Manager
- Network connectivity between server and both devices

## 🔧 Installation Steps

### 1. Server Setup

```bash
# Clone or copy the application files to your server
cd /opt/seqrng-ctm  # or your preferred directory

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configuration Setup

Choose one of the following configuration methods:

#### Option A: Interactive Setup (Recommended for first-time setup)
```bash
python config.py --setup
```

#### Option B: Using Environment Variables
```bash
# Set environment variables (recommended for production)
export SEQRNG_IP_ADDRESS="your.seqrng.ip.address"
export SEQRNG_API_TOKEN="your_seqrng_token"
export CTM_IP_ADDRESS="your.ctm.ip.address"
export CTM_USERNAME="your_ctm_username"
export CTM_PASSWORD="your_ctm_password"
export CTM_DOMAIN="your_ctm_domain"
```

#### Option C: Using .env File
```bash
# Create example files
python config.py --create-examples

# Copy and edit the .env file
cp .env.example .env
nano .env  # Edit with your actual credentials
```

#### Option D: Configuration File
```bash
# Create example files
python config.py --create-examples

# Copy and edit the config file
cp config.json.example config.json
nano config.json  # Edit with your actual credentials
```

### 3. Security Hardening

#### File Permissions
```bash
# Restrict access to configuration files
chmod 600 .env config.json
chown app_user:app_group .env config.json
```

#### Firewall Configuration
```bash
# Allow only necessary ports
# SeQRNG API (adjust port as needed)
ufw allow from <CTM_IP> to any port 443
# CTM API  
ufw allow from <SEQRNG_IP> to any port 443
```

## 🔐 Security Considerations

### ✅ Implemented Security Features

1. **No Hardcoded Credentials**: All sensitive data is externalized
2. **Environment Variable Support**: Secure configuration management
3. **Configuration Validation**: Automatic validation of required settings
4. **Multiple Configuration Sources**: Flexibility for different deployment scenarios
5. **Git Ignore**: Sensitive files excluded from version control

### 🔒 Additional Security Recommendations

1. **Run as Non-Root User**
   ```bash
   # Create dedicated user
   useradd -r -s /bin/false seqrng-ctm
   chown -R seqrng-ctm:seqrng-ctm /opt/seqrng-ctm
   ```

2. **Network Segmentation**
   - Place application in DMZ or isolated network segment
   - Use VPN for remote access
   - Implement network monitoring

3. **Certificate Management**
   - Use proper SSL/TLS certificates (currently disabled with verify=False)
   - Implement certificate pinning if possible

4. **Logging and Monitoring**
   - Enable application logging
   - Monitor for failed authentication attempts
   - Set up alerts for unusual activity

5. **Backup and Recovery**
   - Regular backup of configuration files (encrypted)
   - Document recovery procedures
   - Test disaster recovery scenarios

## 🏃‍♂️ Running the Application

### Development/Testing
```bash
python SeQRNG_CTM_1.4.py
```

### Production with Systemd Service
Create `/etc/systemd/system/seqrng-ctm.service`:

```ini
[Unit]
Description=SeQRNG CTM Integration Service
After=network.target

[Service]
Type=simple
User=seqrng-ctm
Group=seqrng-ctm
WorkingDirectory=/opt/seqrng-ctm
Environment=PYTHONPATH=/opt/seqrng-ctm
ExecStart=/usr/bin/python3 /opt/seqrng-ctm/SeQRNG_CTM_1.4.py
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/seqrng-ctm/logs

[Install]
WantedBy=multi-user.target
```

Start the service:
```bash
systemctl daemon-reload
systemctl enable seqrng-ctm
systemctl start seqrng-ctm
```

## 📊 Configuration Priority Order

The application loads configuration in this order (highest to lowest precedence):

1. **Environment Variables** (highest priority)
2. **.env File**
3. **config.json File** (lowest priority)

## 🔍 Troubleshooting

### Configuration Issues
```bash
# Test configuration
python config.py --create-examples
python -c "from config import get_config; config=get_config(); config.validate_config()"
```

### Connection Issues
```bash
# Test network connectivity
ping <SEQRNG_IP>
ping <CTM_IP>
telnet <SEQRNG_IP> 443
telnet <CTM_IP> 443
```

### Permission Issues
```bash
# Check file permissions
ls -la .env config.json
# Check process permissions
ps aux | grep python
```

## 📱 Monitoring and Maintenance

### Log Monitoring
- Monitor application logs for errors
- Set up log rotation
- Monitor disk space

### Performance Monitoring
- Monitor CPU and memory usage
- Track response times
- Monitor network connections

### Security Monitoring
- Monitor failed authentication attempts
- Track configuration changes
- Monitor for unusual network activity

## 🆘 Emergency Procedures

### Lost Configuration
1. Stop the application
2. Recreate configuration using interactive setup
3. Verify configuration
4. Restart application

### Security Breach
1. Immediately stop the application
2. Change all credentials (SeQRNG tokens, CTM passwords)
3. Review logs for suspicious activity
4. Update configuration with new credentials
5. Restart application with monitoring

### System Failure
1. Check system resources (disk, memory, network)
2. Review application logs
3. Verify configuration files
4. Restart application
5. If issues persist, contact support

## 📞 Support Information

For technical support:
- Check logs in `/opt/seqrng-ctm/logs/`
- Review this deployment guide
- Contact system administrator
- Document any issues for troubleshooting 